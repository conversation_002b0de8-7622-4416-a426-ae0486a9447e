# Build Stage
FROM node:20.10.0-bullseye AS build

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application source code
COPY . .

# Build the Vue.js app
RUN npm run build

# Final Stage
FROM node:20.10.0-bullseye-slim

# Install serve globally
RUN npm install -g serve

# Copy the built app from the build stage
COPY --from=build /app/dist /app/dist

# Set the working directory
WORKDIR /app

# Expose port 80 for the app
EXPOSE 80

# Start the app using serve
CMD ["serve", "-s", "dist", "-l", "80"]
