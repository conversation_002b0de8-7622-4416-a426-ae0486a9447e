version: "3.9"

services:
  postgres:
    container_name: margaret_db
    image: postgres:14
    restart: unless-stopped
    env_file: fishbush/.env
    environment:
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_DB: ${DATABASE_NAME}
    ports:
      - "5432:5432"
    volumes:
      - margaret-db-data-dev:/var/lib/postgresql/data
    networks:
      - margaret_db_network

volumes:
  strapi-db-data-dev:

networks:
  margaret_db_network:
    driver: bridge
